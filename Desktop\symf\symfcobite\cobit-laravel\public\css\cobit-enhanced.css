/* COBIT Enhanced Styles */

/* Animations pour les indicateurs de validation */
.status-indicator {
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Styles pour les tableaux d'objectifs */
.objectives-table {
    max-height: 300px;
    overflow-y: auto;
}

.objectives-table::-webkit-scrollbar {
    width: 6px;
}

.objectives-table::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.objectives-table::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.objectives-table::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Styles pour l'IA Bundle */
.ai-bundle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.ai-recommendation {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Styles pour les métriques */
.metric-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Styles pour les graphiques */
.chart-container {
    position: relative;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Styles pour les onglets avec indicateurs */
.df-tab {
    position: relative;
    transition: all 0.3s ease;
}

.df-tab:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.df-tab.validated {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.df-tab.in-progress {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

/* Styles pour les recommandations */
.recommendation-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.recommendation-item.critical {
    border-left-color: #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1), transparent);
}

.recommendation-item.warning {
    border-left-color: #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.1), transparent);
}

.recommendation-item.success {
    border-left-color: #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.1), transparent);
}

.recommendation-item.improvement {
    border-left-color: #3b82f6;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), transparent);
}

/* Styles pour le canvas de résultats */
.results-canvas {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.results-header {
    text-align: center;
    margin-bottom: 40px;
}

.results-header h2 {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 800;
}

/* Styles pour les tableaux de bord */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

/* Styles pour les badges de statut */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.validated {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-badge.in-progress {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-badge.pending {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
}

/* Styles pour les priorités IA */
.priority-high {
    color: #10b981;
    font-weight: 700;
}

.priority-medium {
    color: #f59e0b;
    font-weight: 600;
}

.priority-low {
    color: #6b7280;
    font-weight: 500;
}

.priority-critical {
    color: #ef4444;
    font-weight: 800;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* Styles responsifs */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .df-tab {
        font-size: 0.875rem;
        padding: 8px 12px;
    }
    
    .results-header h2 {
        font-size: 2rem;
    }
    
    .chart-container {
        padding: 15px;
    }
}

/* Styles pour les tooltips */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
}

/* Styles pour les graphiques radar */
.radar-chart-container {
    position: relative;
    height: 400px;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Animation d'entrée pour les éléments */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Styles pour les boutons d'action */
.action-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Styles pour les indicateurs de progression */
.progress-ring {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(#10b981 var(--progress), #e5e7eb 0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-ring::before {
    content: '';
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.progress-text {
    position: relative;
    z-index: 1;
    font-weight: 700;
    font-size: 0.875rem;
}
