/* COBIT 2019 Design Toolkit - Styles personnalisés */

:root {
    --primary: #1E40AF;
    --secondary: #3B82F6;
    --accent: #10B981;
    --dark: #1F2937;
    --light: #F9FAFB;
    --danger: #DC2626;
    --warning: #F59E0B;
    --success: #059669;
}

/* Animations */
@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes slideIn {
    from { 
        transform: translateX(-100%); 
    }
    to { 
        transform: translateX(0); 
    }
}

@keyframes pulse {
    0%, 100% { 
        transform: scale(1); 
    }
    50% { 
        transform: scale(1.05); 
    }
}

/* Utilitaires */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse-hover:hover {
    animation: pulse 0.3s ease-in-out;
}

/* Cards amélio<PERSON>es */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 12px 12px 0 0;
}

/* Sliders personnalisés */
.slider-container {
    margin: 15px 0;
    padding: 10px;
    border-radius: 8px;
    background: #f8fafc;
    transition: background-color 0.2s;
}

.slider-container:hover {
    background: #f1f5f9;
}

.slider {
    width: 100%;
    height: 8px;
    border-radius: 5px;
    background: linear-gradient(to right, #e5e7eb 0%, var(--primary) 100%);
    outline: none;
    opacity: 0.8;
    transition: opacity 0.2s;
    cursor: pointer;
}

.slider:hover {
    opacity: 1;
}

.slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary);
    cursor: pointer;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: all 0.2s;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(0,0,0,0.3);
}

.slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--primary);
    cursor: pointer;
    border: 3px solid #ffffff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: all 0.2s;
}

/* Boutons améliorés */
.btn {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(30, 64, 175, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1D4ED8 0%, #2563EB 100%);
    box-shadow: 0 6px 12px rgba(30, 64, 175, 0.4);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(107, 114, 128, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4B5563 0%, #374151 100%);
    box-shadow: 0 6px 12px rgba(107, 114, 128, 0.4);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent) 0%, var(--success) 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success) 0%, #047857 100%);
    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger) 0%, #B91C1C 100%);
    color: white;
    box-shadow: 0 4px 6px rgba(220, 38, 38, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #B91C1C 0%, #991B1B 100%);
    box-shadow: 0 6px 12px rgba(220, 38, 38, 0.4);
    transform: translateY(-2px);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Progress bar */
.progress-step {
    transition: all 0.3s ease;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.progress-step.active::after {
    transform: translateX(100%);
}

/* Charts containers */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
    padding: 10px;
    background: #fafafa;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    padding: 16px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, var(--accent) 0%, var(--success) 100%);
}

.notification.error {
    background: linear-gradient(135deg, var(--danger) 0%, #B91C1C 100%);
}

.notification.warning {
    background: linear-gradient(135deg, var(--warning) 0%, #D97706 100%);
}

.notification.info {
    background: linear-gradient(135deg, var(--secondary) 0%, var(--primary) 100%);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

table {
    border-collapse: separate;
    border-spacing: 0;
}

table th {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
}

table tr:hover {
    background: #f8fafc;
}

/* Priority badges */
.priority-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.priority-high {
    background: rgba(220, 38, 38, 0.1);
    color: #DC2626;
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.priority-low {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-header {
        padding: 15px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.875rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100px);
        max-width: none;
    }
    
    .notification.show {
        transform: translateY(0);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .card-header {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        border-bottom-color: #4b5563;
    }
    
    .slider-container {
        background: #374151;
    }
    
    .slider-container:hover {
        background: #4b5563;
    }
}
